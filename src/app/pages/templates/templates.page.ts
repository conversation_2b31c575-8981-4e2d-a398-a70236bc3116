import { Compo<PERSON>, NgZone, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonRouterOutlet, MenuController, ModalController, NavController } from '@ionic/angular/standalone';
import { <PERSON>ertController } from '@ionic/angular/standalone';
import { IonContent, IonHeader, IonTitle, IonToolbar, IonMenuButton, IonButtons, IonButton , PopoverController, IonSearchbar, IonLabel, IonList, IonListHeader, IonItemDivider, IonThumbnail, IonSpinner, IonProgressBar  } from '@ionic/angular/standalone';
import { IonItem,  } from '@ionic/angular/standalone';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
// Import RigActions from its module (update the path as needed)
import * as RigActions from 'src/app/store/store.actions';
import { Store } from '@ngrx/store';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { selectAllTemplates, selectPrefilledData, selectProgressPercentage, selectRigData, selectRigLoadedFromDb, selectTemplatesLoadedFromDb } from 'src/app/store/store.selector';
import { BehaviorSubject, combineLatest, distinctUntilChanged, filter, firstValueFrom, map, Observable, startWith, switchMap, take, tap } from 'rxjs';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { Router } from '@angular/router';
import { DataService } from 'src/app/services/data.service';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import { IonSkeletonText } from '@ionic/angular/standalone';
import { UtilityService } from 'src/app/services/utility.service';
import { AlertService } from 'src/app/services/alert.service';
import { EventsService } from 'src/app/services/events.service';
import { STMRDetailsPage } from '../stmr-details/stmr-details.page';
import { AppConstants } from 'src/app/constants/appConstants';
import { STMR_HEADER } from 'src/models/STMR_HEADER';
import moment from 'moment';
import { CustomDatePipe } from 'src/app/pipes/custom-date.pipe';

export enum ModeOfOperation {
  CreateForm,
  AddFormToTopic
}

declare var ump: any; // Assuming ump is a global object for database operations
@Component({
  selector: 'app-templates',
  templateUrl: './templates.page.html',
  styleUrls: ['./templates.page.scss'],
  standalone: true,
  imports: [CustomDatePipe, IonSkeletonText, IonProgressBar, IonButtons, IonLabel  ,IonItem , IonThumbnail, IonItemDivider, IonList, IonButton, IonMenuButton,  IonContent, IonHeader, IonTitle, IonToolbar ,CommonModule, FormsModule, TranslateModule , IonSearchbar]

})
export class TemplatesPage implements OnInit {
  mode: ModeOfOperation = ModeOfOperation.CreateForm;
  timestampOfLastTap: number = 0;
  filter = { searchText: '' };
  templates: any[] = []; // replace with your actual data structure
  newTemplates: any[] = [];

 
    rigData$!: Observable<RIG_HEADER| null>;
    templates$!: Observable<TEMPLATE_HEADER[]| null>;
    progress$!: Observable<number>;
    prefilledData$!: Observable<any>
    skeletonArray = Array(10); // default to 5 placeholders

    newCategories: { CAT_ID: string, CATEGORY_DESC: string }[] = [];   
    isLoading = true; 
    private searchTermSubject = new BehaviorSubject<string>('');

  constructor(     
    private routerOutlet: IonRouterOutlet,
    private menuCtrl: MenuController , 
    private translate: TranslateService , 
    public alertController: AlertController,
    private ngZone: NgZone,
    private modalController: ModalController,
    public events: EventsService, 
    private store: Store, 
    private unviredSDK: UnviredCordovaSDK , 
    private dataService: DataService, 
    private utilityService: UtilityService,
    private navCtrl: NavController,
    private alertService: AlertService,
    private popoverController: PopoverController, 
    private router: Router
  ) {
    this.rigData$ = this.store.select(selectRigLoadedFromDb);
   this.routerOutlet.swipeGesture = false;
      this.menuCtrl.swipeGesture(true)
  }

  ngOnInit() {
    // Dispatch action to load templates
    this.store.dispatch(RigActions.loadAllTemplatesFromDb());
  
    // Track the loading state of the templates
    this.store.select(selectTemplatesLoadedFromDb).subscribe(loaded => {
      console.log('Templates loaded from DB:', loaded);
      this.isLoading = !loaded;  // Set loading state based on the `loadedFromDb` flag
    });
  
    // Subscribe to the templates data
    this.templates$ = this.store.select(selectAllTemplates).pipe(
      tap(templates => {
        console.log('Loaded templates:', templates);
      })
    );


    

 
  
  }


  ionViewWillEnter() {
    // this.dataService.getCompanyHeaderData();
this.prefilledData$ = this.store.select(selectPrefilledData);

   this.progress$ = this.store.select(selectProgressPercentage);

  // Subscribe separately for side effects
  this.progress$
    .pipe(
      tap(progress => console.log('Progress emitted:', progress)),
      filter(progress => Math.round(progress) >= 100),
      tap(() => console.log('100 percent completed')),
      take(1)
    )
    .subscribe(() => {
      console.log('Dispatching loadAllTemplatesFromDb');
      this.store.dispatch(RigActions.loadAllTemplatesFromDb());
    });

  }

  
  
  



  groupedTemplates$ = combineLatest([
    this.store.select(selectAllTemplates),
    this.searchTermSubject.asObservable().pipe(startWith('')) // reactive search input
  ]).pipe(
    map(([templates, searchTerm]) => {
      if (!templates) return [];
  
      const filtered = templates.filter((template: TEMPLATE_HEADER) => {
        const term = searchTerm.toLowerCase();
        return (
          template.NAME?.toLowerCase().includes(term) ||
          template.CATEGORY_DESC?.toLowerCase().includes(term)
        );
      });
  
      const grouped = filtered.reduce((acc, t) => {
        const category = t.CATEGORY_DESC || 'Uncategorized';
        if (!acc[category]) acc[category] = [];
        acc[category].push(t);
        return acc;
      }, {} as { [key: string]: any[] });
  
      return Object.entries(grouped).map(([category, items]) => ({
        category,
        items
      }));
    })
  );
  

  returnDisplayDate(timestamp: number, format: string = '', fallback: string = '-'): string {
    if (!timestamp) return fallback;
  
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric'
    }); // You can make this dynamic with format if needed
  }
  

  onSearchChange(term: string | null | undefined) {
    this.searchTermSubject.next(term?.toLowerCase() ?? '');
  }


  refreshData(){
 
   
    this.store.dispatch(RigActions.loadRigFromDb());

    this.store.select(selectRigLoadedFromDb).pipe(
      tap((loaded: any) => console.log('loadedFromDb:', loaded)), // Add this line
      filter((loaded): loaded is boolean => !!loaded),
      switchMap(() => this.store.select(selectRigData).pipe(take(1))),
      take(1)
    ).subscribe(async (rigData) => {
      console.log('RigData from DB:', rigData);
    
      const company = rigData!.COMP_CODE;
      const rigNo = rigData!.RIG_NO;
      const rigType = rigData!.RIG_TYPE;
      const rigSubType = rigData!.RIG_SUB_TYPE;
      if (!rigData || !rigData.RIG_NO) {
        console.warn('RigData is null or RIG_NO missing — opening site popup');
        // await this.openSiteNumberPopup();
      }

  
    });


    this.store.dispatch(RigActions.loadAllTemplatesFromDb());



    this.templates$ = this.store.select(selectAllTemplates).pipe(
      tap(loaded => {
        console.log('Templates loaded from DB:', loaded);
        this.isLoading = !loaded;  // Set loading state based on the `loaded` flag
      })
    )

   
 
  }


    retryDownload(event: any , template: TEMPLATE_HEADER){

  }

  hasError(id: number){}

  createForm(event: any , template: TEMPLATE_HEADER) {
    // console.log('Creating form for template:', template);
    // this.store.dispatch(RigActions.createForm({ template }));
    // this.router.navigate(['/forms']);
  }
  


  
  round(value: number): number {
    return Math.round(value);
  }


  mapCategories(data: any[]) {
    // your logic to map categories
  }

  getDataFromDB() {
    // fetch fresh data from DB
  }
  
 async createSTMR() {
    const timestamp = new Date().getTime();

    if (timestamp - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      this.unviredSDK.logInfo('TemplatesPage', 'createSTMR()', 'Ignoring double tap');
      return;
    }

    this.timestampOfLastTap = timestamp;

    const isDownloaded = await this.utilityService.checkIfAllInitDataDownloaded();
    if (!isDownloaded) {
      this.alertService.showAlert(
        this.translate.instant('Please wait'),
        this.translate.instant(
          'Customization data is being downloaded. You can create a new STMR once this data is downloaded.'
        )
      );
      return;
    }

     const alert = await this.alertController.create({
    header: this.translate.instant('Create STMR'),
    message: AppConstants.CREATE_STMR_CONFIRM_MSG,
    buttons: [
      {
        text: this.translate.instant('Create STMR'),
        handler: async () => {
          const stmrHeader = await this.createNewSTMRObject();

          const modal = await this.modalController.create({
            component: STMRDetailsPage,
            componentProps: {
              stmr: stmrHeader,
              isNewSTMR: true,
            },
            // breakpoints: [1],
            // initialBreakpoint: 1,
            // showBackdrop: true,
            // backdropDismiss: false,
            cssClass: 'full-screen-modal', 
          });

          await modal.present();
        },
      },
      {
        text: this.translate.instant('Cancel'),
        role: 'cancel',
      },
    ],
  });

  await alert.present();

  const isLogoSet = await this.utilityService.checkCompanyLogoStatus();
  if (!isLogoSet) {
    document.dispatchEvent(new CustomEvent(AppConstants.EVENT_COM_LOGO_LOAD));
  }
}

  async createNewSTMRObject(): Promise<STMR_HEADER> {
    const stmrHeader = new STMR_HEADER();
    const autoFillData = await firstValueFrom(this.dataService.getPrefillData());
    const count = await this.getSTMRsCount();

    const matchAutoFillKeys = ['RIG_NO', 'COMPANY', 'RIG_TYPE', 'RIG_SUB_TYPE'];
    for (let key of matchAutoFillKeys) {
      (stmrHeader as any)[key] = autoFillData[key];
    }

    stmrHeader.COMPANY = autoFillData.COMP_CODE;
    stmrHeader.SUBM_BY = autoFillData.USER_ID || '';
    stmrHeader.OPERATOR = autoFillData.OPERATOR?.[0]?.NAME || '';
    stmrHeader.WELL_LOC = autoFillData.OPERATOR?.[0]?.WELL_NAME || '';
    stmrHeader.LID = this.utilityService.guid32();
    stmrHeader.SHIFT_TIME = moment.utc().unix();
    stmrHeader.CRTD_ON = stmrHeader.SHIFT_TIME;
    stmrHeader.CRTD_BY = autoFillData.USER_ID || '';
    stmrHeader.SHIFT = '';
    stmrHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    stmrHeader.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    stmrHeader.STMR_STATUS = AppConstants.VAL_FORM_STATUS.INPR;
    stmrHeader.STMR_ID = 'New' + this.utilityService.genStmrId(count, stmrHeader.RIG_NO);

    return stmrHeader;
  }

  async getSTMRsCount(): Promise<number> {
  try {
    const result: any = await this.unviredSDK.dbExecuteStatement(
      `SELECT COUNT(*) as COUNT FROM ${AppConstants.TABLE_STMR_HEADER}`
    );

    if (result?.rows?.length) {
      return result.rows[0]?.COUNT ?? 0;
    } else {
      this.unviredSDK.logError('TemplatesPage', 'getSTMRsCount', 'No result returned');
      return 0;
    }
  } catch (error) {
    this.unviredSDK.logError('TemplatesPage', 'getSTMRsCount', JSON.stringify(error));
    throw error;
  }
}
}





