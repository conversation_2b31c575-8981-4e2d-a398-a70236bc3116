import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Alert<PERSON>ontroller, PopoverController } from '@ionic/angular/standalone';
import { IonButton, IonInput, IonItem, ModalController } from '@ionic/angular/standalone';
import { DataService } from 'src/app/services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { Store } from '@ngrx/store';

import { AppState } from 'src/app/store/app.state'; // Import AppState type
import { clearRigState, switchRig } from 'src/app/store/store.actions';
import { selectRigDataFromDb, selectRigError } from 'src/app/store/store.selector';
import { filter, Observable, take } from 'rxjs';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { CommonModule } from '@angular/common';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import * as RigActions from 'src/app/store/store.actions';
@Component({
  selector: 'app-site-rig-modal',
  templateUrl: './site-rig-modal.component.html',
  styleUrls: ['./site-rig-modal.component.scss'],
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [  FormsModule , CommonModule, IonInput ]
})
export class SiteRigModalComponent  {

 siteNumber: string = '';
 rigData$!: Observable<RIG_HEADER | null>;
errorMessage: string | null = null;
  constructor(
    private popoverController: PopoverController,
    private dataService: DataService,
    private device: Device,
    private busyIndicatorService: BusyIndicatorService,
    private alertController: AlertController,
    private store: Store<AppState> // Specify AppState here
  ) {
    console.log('device', this.device.platform);

   
  }
  

  async submit() {
    console.log(this.siteNumber);
   if (this.rigData$) {
  this.rigData$.pipe(take(1)).subscribe((data) => {
    if (this.siteNumber === data?.RIG_NO) {
      this.errorMessage = 'Site number should not be the same as the current one';
    }
  });
} else {
  console.warn('rigData$ is not initialized yet');
 
}
    this.busyIndicatorService.displayBusyIndicator('Switching site' )
    const rigId = this.siteNumber.trim();
  if (!rigId) {
    console.warn('Site number is empty – not proceeding');
    return;
  }
  
    if (this.siteNumber.trim()) {
      const rigId = this.siteNumber.trim();
  
      // Optional: clear previous state if needed
      this.store.dispatch(clearRigState());
   
      this.store.dispatch(switchRig({ rigId, deviceId: this.device.uuid }));
  
      this.store.select(selectRigDataFromDb).pipe(
        filter((rigData): rigData is RIG_HEADER => !!rigData), // narrow the type
        take(1) // only once after data is available
      ).subscribe(async (rigHeader) => {
        this.busyIndicatorService.dismissBusyIndicator()
        this.busyIndicatorService.presentToast('A request to download all data has been placed...Please wait')
        console.log('✅ Rig switched successfully:', rigHeader);
          this.store.dispatch(RigActions.loadAllFormsFromDb());
        this.dataService.getAllTemplatesFromDB(rigHeader.COMP_CODE , rigHeader.RIG_NO , rigHeader.RIG_TYPE , rigHeader.RIG_SUB_TYPE);
        this.popoverController.dismiss(rigHeader); // dismiss with rig data
      });
      
      this.store.select(selectRigError)
    .pipe(filter(error => !!error))
    .subscribe(error => {
      this.busyIndicatorService.dismissBusyIndicator()
      // console.error('❌ Error while switching rig:', error);
      this.errorMessage = error; // bind to template
    });
    }
  }



  dismiss() {
     
    this.popoverController.dismiss({ data: 'RIG' });
  }
  

}
